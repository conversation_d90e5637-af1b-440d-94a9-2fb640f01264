swagger: '2.0'
info:
  title: Legal Database API Gateway
  description: Public API Gateway for Legal Database services
  version: 1.0.0
host: legal-database-gateway-1k6gjpoj.uc.gateway.dev
schemes:
  - https
produces:
  - application/json

# Security definitions for JWT authentication
securityDefinitions:
  jwt_auth:
    type: api<PERSON>ey
    name: Authorization
    in: header
    x-google-issuer: "https://anwefmklplkjxkmzpnva.supabase.co/auth/v1"
    x-google-jwks_uri: "https://anwefmklplkjxkmzpnva.supabase.co/auth/v1/keys"
    x-google-audiences: "anwefmklplkjxkmzpnva"

paths:
  # Public endpoints (no authentication required)
  /:
    get:
      summary: API Root
      operationId: api_root
      security: []  # explicitly public
      x-google-backend:
        address: https://legal-api-stg-gfunh6mfpa-uc.a.run.app/
        jwt_audience: legal-api-stg-gfunh6mfpa-uc.a.run.app
      responses:
        '200':
          description: API information

  /health:
    get:
      summary: Health Check
      operationId: health_check
      security: []  # explicitly public
      x-google-backend:
        address: https://legal-api-stg-gfunh6mfpa-uc.a.run.app/health
      responses:
        '200':
          description: Service health status

  /metrics:
    get:
      summary: Prometheus Metrics
      operationId: metrics
      security: []  # explicitly public
      x-google-backend:
        address: https://legal-api-stg-gfunh6mfpa-uc.a.run.app/metrics
      responses:
        '200':
          description: Prometheus metrics

  /v0/graph/sample:
    get:
      summary: Sample Graph Data
      operationId: sample_graph
      security: []  # explicitly public
      x-google-backend:
        address: https://legal-api-stg-gfunh6mfpa-uc.a.run.app/v0/graph/sample
      responses:
        '200':
          description: Sample React-Flow compatible graph data

  /jobs/authority/status:
    get:
      summary: Authority Job Status
      operationId: authority_status
      security: []  # explicitly public
      x-google-backend:
        address: https://legal-api-stg-gfunh6mfpa-uc.a.run.app/jobs/authority/status
      responses:
        '200':
          description: Authority calculation job status

  # Protected endpoints (require JWT authentication)
  /v0/search:
    get:
      summary: Search Documents
      operationId: search_documents
      security:
        - jwt_auth: []
      parameters:
        - name: q
          in: query
          type: string
          required: true
          description: Search query
        - name: limit
          in: query
          type: integer
          default: 10
          description: Maximum number of results
        - name: offset
          in: query
          type: integer
          default: 0
          description: Offset for pagination
      x-google-backend:
        address: https://legal-api-stg-gfunh6mfpa-uc.a.run.app/v0/search
      responses:
        200:
          description: Search results
        401:
          description: Unauthorized
          
  /v0/recommend/{document_id}:
    get:
      summary: Get Document Recommendations
      operationId: get_recommendations
      security:
        - jwt_auth: []
      parameters:
        - name: document_id
          in: path
          type: string
          required: true
          description: Document ID
        - name: limit
          in: query
          type: integer
          default: 10
          description: Maximum number of recommendations
      x-google-backend:
        address: https://legal-api-stg-gfunh6mfpa-uc.a.run.app/v0/recommend/{document_id}
      responses:
        200:
          description: Document recommendations
        401:
          description: Unauthorized
        404:
          description: Document not found
          
  /v0/graph:
    get:
      summary: Get Graph Data
      operationId: get_graph
      security:
        - jwt_auth: []
      parameters:
        - name: id
          in: query
          type: string
          required: true
          description: Starting document ID
        - name: depth
          in: query
          type: integer
          default: 2
          minimum: 1
          maximum: 3
          description: Graph traversal depth
        - name: direction
          in: query
          type: string
          default: both
          enum: [both, in, out]
          description: Citation direction
        - name: max_nodes
          in: query
          type: integer
          default: 200
          maximum: 500
          description: Maximum number of nodes
        - name: node_types
          in: query
          type: array
          items:
            type: string
          description: Filter by node types
      x-google-backend:
        address: https://legal-api-stg-gfunh6mfpa-uc.a.run.app/v0/graph
      responses:
        200:
          description: React-Flow compatible graph data
        401:
          description: Unauthorized
        400:
          description: Invalid parameters

  # Admin endpoints (require JWT authentication)
  /jobs/authority/calculate:
    post:
      summary: Trigger Authority Calculation
      operationId: trigger_authority_calc
      security:
        - jwt_auth: []
      parameters:
        - name: body
          in: body
          required: true
          schema:
            type: object
            properties:
              force:
                type: boolean
                default: false
                description: Force calculation even if recent
              dry_run:
                type: boolean
                default: false
                description: Dry run without actual calculation
      x-google-backend:
        address: https://legal-api-stg-gfunh6mfpa-uc.a.run.app/jobs/authority/calculate
      responses:
        200:
          description: Authority calculation triggered
        401:
          description: Unauthorized
        500:
          description: Internal server error
