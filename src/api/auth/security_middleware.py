"""
Security middleware for additional protection layers.
Implements request validation, IP filtering, and security headers.
"""

import logging
import time
from typing import Set, Optional
from fastapi import Request, HTTPException
from fastapi.responses import Response
import ipaddress

logger = logging.getLogger(__name__)


class SecurityMiddleware:
    """
    Advanced security middleware for FastAPI.
    
    Features:
    - Request source validation
    - Security headers
    - IP allowlisting for admin endpoints
    - Request size limits
    - Suspicious activity detection
    """
    
    def __init__(
        self,
        allowed_gateway_ips: Optional[Set[str]] = None,
        max_request_size: int = 10 * 1024 * 1024,  # 10MB
        admin_allowed_ips: Optional[Set[str]] = None
    ):
        self.allowed_gateway_ips = allowed_gateway_ips or set()
        self.max_request_size = max_request_size
        self.admin_allowed_ips = admin_allowed_ips or set()
        
        # Google Cloud Run and API Gateway IP ranges (update as needed)
        self.google_cloud_ranges = {
            "**********/16",    # Google Cloud Load Balancer
            "***********/22",   # Google Cloud Load Balancer
            "*********/14",     # Google Cloud Run
            "**************/32" # API Gateway (example)
        }
    
    def is_request_from_gateway(self, client_ip: str) -> bool:
        """
        Check if request comes from API Gateway or allowed sources.
        
        Args:
            client_ip: Client IP address
            
        Returns:
            True if request is from allowed source
        """
        if not client_ip:
            return False
            
        try:
            client_addr = ipaddress.ip_address(client_ip)
            
            # Check specific allowed IPs
            if client_ip in self.allowed_gateway_ips:
                return True
                
            # Check Google Cloud IP ranges
            for cidr in self.google_cloud_ranges:
                if client_addr in ipaddress.ip_network(cidr):
                    return True
                    
            return False
            
        except ValueError:
            logger.warning(f"Invalid IP address: {client_ip}")
            return False
    
    def add_security_headers(self, response: Response) -> Response:
        """
        Add security headers to response.
        
        Args:
            response: FastAPI response object
            
        Returns:
            Response with security headers
        """
        # Security headers
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        response.headers["Content-Security-Policy"] = "default-src 'self'"
        
        # API-specific headers
        response.headers["X-API-Version"] = "1.0.0"
        response.headers["X-Powered-By"] = "Legal-Database-API"
        
        return response
    
    def validate_request_size(self, request: Request) -> None:
        """
        Validate request size to prevent DoS attacks.
        
        Args:
            request: FastAPI request object
            
        Raises:
            HTTPException: If request is too large
        """
        content_length = request.headers.get("content-length")
        if content_length and int(content_length) > self.max_request_size:
            raise HTTPException(
                status_code=413,
                detail=f"Request too large. Maximum size: {self.max_request_size} bytes"
            )
    
    def check_admin_access(self, request: Request, client_ip: str) -> None:
        """
        Check if admin endpoints are accessed from allowed IPs.
        
        Args:
            request: FastAPI request object
            client_ip: Client IP address
            
        Raises:
            HTTPException: If access is denied
        """
        # Admin endpoints that require IP allowlisting
        admin_paths = [
            "/jobs/authority/calculate",
            "/admin/",
            "/internal/"
        ]
        
        if any(request.url.path.startswith(path) for path in admin_paths):
            if self.admin_allowed_ips and client_ip not in self.admin_allowed_ips:
                logger.warning(f"Admin access denied for IP: {client_ip}")
                raise HTTPException(
                    status_code=403,
                    detail="Admin access restricted to allowed IPs"
                )
    
    def detect_suspicious_activity(self, request: Request, client_ip: str) -> None:
        """
        Detect suspicious request patterns.
        
        Args:
            request: FastAPI request object
            client_ip: Client IP address
        """
        # Log suspicious patterns
        suspicious_patterns = [
            "/../",           # Path traversal
            "<script",        # XSS attempts
            "union select",   # SQL injection
            "eval(",          # Code injection
            "javascript:",    # XSS
        ]
        
        request_str = str(request.url).lower()
        for pattern in suspicious_patterns:
            if pattern in request_str:
                logger.warning(
                    f"Suspicious request from {client_ip}: {pattern} in {request.url}"
                )
                break
    
    async def __call__(self, request: Request, call_next):
        """
        Security middleware handler.
        
        Args:
            request: FastAPI request object
            call_next: Next middleware in chain
            
        Returns:
            Response with security enhancements
        """
        start_time = time.time()
        
        # Get client IP
        client_ip = self.get_client_ip(request)
        
        # Skip security checks for health endpoints
        if request.url.path in ["/health", "/metrics"]:
            response = await call_next(request)
            return self.add_security_headers(response)
        
        try:
            # Validate request size
            self.validate_request_size(request)
            
            # Check if request comes from API Gateway (in production)
            # Skip this check in development
            if not self.is_development_mode():
                if not self.is_request_from_gateway(client_ip):
                    logger.warning(f"Direct access attempt from {client_ip}")
                    raise HTTPException(
                        status_code=403,
                        detail="Direct access not allowed. Use API Gateway."
                    )
            
            # Check admin access restrictions
            self.check_admin_access(request, client_ip)
            
            # Detect suspicious activity
            self.detect_suspicious_activity(request, client_ip)
            
            # Process request
            response = await call_next(request)
            
            # Add security headers
            response = self.add_security_headers(response)
            
            # Log successful request
            process_time = time.time() - start_time
            logger.info(
                f"Security check passed - {request.method} {request.url.path} "
                f"from {client_ip} - {process_time:.3f}s"
            )
            
            return response
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Security middleware error: {e}")
            raise HTTPException(status_code=500, detail="Internal security error")
    
    def get_client_ip(self, request: Request) -> str:
        """
        Get client IP address from request headers.
        
        Args:
            request: FastAPI request object
            
        Returns:
            Client IP address
        """
        # Check various headers for real IP
        headers_to_check = [
            "x-forwarded-for",
            "x-real-ip",
            "x-client-ip",
            "cf-connecting-ip"  # Cloudflare
        ]
        
        for header in headers_to_check:
            ip = request.headers.get(header)
            if ip:
                # Take first IP if comma-separated
                return ip.split(",")[0].strip()
        
        # Fallback to direct client IP
        return request.client.host if request.client else "unknown"
    
    def is_development_mode(self) -> bool:
        """
        Check if running in development mode.
        
        Returns:
            True if in development mode
        """
        import os
        return os.getenv("ENVIRONMENT", "production").lower() in ["development", "dev", "local"]


# Global security middleware instance
security_middleware = SecurityMiddleware()
